<html>

<head>
    <style>
        body {
            font-family: sans-serif;
            background-color: #f3f3f3;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        button {
            padding: 10px 20px;
            font-size: 14px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        #recordBtn {
            background-color: #4CAF50;
            color: white;
        }

        #recordBtn:hover {
            background-color: #45a049;
        }

        #recordBtn.recording {
            background-color: #f44336;
        }

        #recordBtn.recording:hover {
            background-color: #da190b;
        }

        #downloadLink {
            padding: 10px 20px;
            background-color: #008CBA;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            display: none;
        }

        #downloadLink:hover {
            background-color: #007aa3;
        }

        .recording-indicator {
            color: #f44336;
            font-weight: bold;
            display: none;
        }
    </style>
</head>

<body>
    <h1>Example Audio Loopback App</h1>

    <audio id="audio" autoplay controls></audio>

    <div class="controls">
        <button id="recordBtn">Start Recording</button>
        <a id="downloadLink" download="system_audio_recording.webm">Download Recording</a>
        <span id="recordingIndicator" class="recording-indicator">● Recording...</span>
    </div>

    <p id="status">Obtaining audio stream...</p>

    <canvas id="canvas"></canvas>
</body>

</html>